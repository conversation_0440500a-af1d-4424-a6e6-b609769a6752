import type {
    DesktopSoftware,
    DesktopSoftwareAppData,
    FactSheetData,
    Field,
    RelationValue,
    ServerSoftware,
    Value
} from '../model'
import dayjs from 'dayjs'
import {getName} from '../config/helpers'
import {PROD, SUB_APPLICATION_OWNER_TECHNICAL, SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL} from '../constants'
import {getAllRoleIdsByUserId, getRingByBusinessCriticality, isUUID} from '../helpers'

export const useLeaniX = () => {
    const validateName = async (prefix: string, name: string) => {
        const existingAppWithName = await getFactSheetByNameAndType(`${prefix}${name}`, 'Application')

        // console.log("EXISTING APPS WITH NAME", existingAppWithName)
        return existingAppWithName.length <= 0
    }

    const getDesktopSoftware = async () => {
        const QUERY = `query allFactSheetsQuery($filter: FilterInput!, $sortings: [Sorting]) {
  allFactSheets(filter: $filter, sort: $sortings) {
    edges {
      node {
        ... on ITComponent {
          id
          displayName
        }
      }
    }
  }
}`
        const VARIABLES = `
{
  "filter": {
    "facetFilters": [
      {
        "facetKey": "FactSheetTypes",
        "operator": "OR",
        "keys": [
          "ITComponent"
        ]
      },
      {
        "facetKey": "category",
        "operator": "OR",
        "keys": [
          "desktop"
        ]
      }
    ]
  },
  "sortings": [
    {
      "key": "displayName",
      "order": "asc"
    }
    ]
}
`

        const data = await lx.executeGraphQL(QUERY, VARIABLES)

        const desktopSoftwares: Value[] = []

        data.allFactSheets.edges.map((prov: { node: { displayName: string, id: string } }) => {
            desktopSoftwares.push({
                label: prov.node.displayName,
                value: prov.node.id
            })
        })

        return desktopSoftwares
    }

    const getProviders = async (onlyName: boolean) => {
        const result = await lx.executeGraphQL(`
                        query {
                  allFactSheets(factSheetType:Provider, sort: {key:"displayName", order:asc}){
                  edges{
                    node{
                      id
                      displayName
                    }
                  }
                  }
                }
        `)

        const providers: Value[] = []

        result.allFactSheets.edges.map((prov: { node: { displayName: string, id: string } }) => {
            providers.push({
                label: prov.node.displayName,
                value: onlyName ? prov.node.displayName : prov.node.id
            })
        })
        return providers
    }

    const createFactSheet = async (name: string, type: string, validateOnly: boolean) => {
        const mutation = `mutation ($input: BaseFactSheetInput!) {
                                                          createFactSheet(input: $input, validateOnly:${validateOnly}) {
                                                            factSheet {
                                                                id
                                                                name
                                                                type
                                                              }
                                                            }
                                                          }
`
        const filter = `{
                                  "input":{
                                    "name":"${name}", 
                                    "type": "${type}"
                                  }
                                }`
        return lx.executeGraphQL(mutation, filter)
    }


    const updateEUT = async (data: any) => {

        console.log("UPDATE EUT")
        const mutation = `
                                    mutation ($patches: [Patch]!) {
                                      result: updateFactSheet(id: "${data.id!}", patches: $patches, validateOnly: false) {
                                        factSheet {
                                          ... on ITComponent {
                                            rev
                                            displayName
                                            name
                                           }
                                        }
                                      }
                                    }`
        const patches: any[] = []

        patches.push({
            op: 'replace',
            path: '/category',
            value: "EUT"
        })

        //DESCRIPTION
        if (data.description && data.description.length > 0) {
            patches.push({
                op: 'replace',
                path: '/description',
                value: data.description
            })
        }
        //toolProgrammingLanguage
        if (data.toolProgrammingLanguage && data.toolProgrammingLanguage.length > 0) {
            patches.push({
                op: 'replace',
                path: '/toolProgrammingLanguage',
                value: data.toolProgrammingLanguage
            })
        }

        //Release
        if (data.release && data.release.length > 0) {
            patches.push({
                op: 'replace',
                path: '/release',
                value: data.release
            })
        }

        // placeOfStorage
        if (data.placeOfStorage && data.placeOfStorage.length > 0) {
            patches.push({
                op: 'replace',
                path: '/placeOfStorage',
                value: data.placeOfStorage
            })
        }

        //CIA RATING
        if(data.infoSecConfidentiality && data.infoSecConfidentiality.length > 0){
            patches.push({
                op: 'replace',
                path: '/infoSecConfidentiality',
                value: data.infoSecConfidentiality
            })
        }

        if(data.infoSecIntegrity && data.infoSecIntegrity.length > 0){
            patches.push({
                op: 'replace',
                path: '/infoSecIntegrity',
                value: data.infoSecIntegrity
            })
        }

        if(data.infoSecAvailability && data.infoSecAvailability.length > 0){
            patches.push({
                op: 'replace',
                path: '/infoSecAvailability',
                value: data.infoSecAvailability
            })
        }

        if(data.securityLevel && data.securityLevel.length > 0){
            patches.push({
                op: 'replace',
                path: '/securityLevel',
                value: data.securityLevel
            })
        }

        if(data.dataProtectionRelevancy && data.dataProtectionRelevancy.length > 0){
            patches.push({
                op: 'replace',
                path: '/dataProtectionRelevancy',
                value: data.dataProtectionRelevancy
            })
        }


        const filter = {
            patches
        }

        console.log("FILTER", filter);

        return lx.executeGraphQL(mutation, JSON.stringify(filter))
    }


    const updateServerSoftwareFactsheet = async (id: string, data: ServerSoftware) => {
        console.log('UPDATE SERVER SOFTWARE', id, data)
        const mutation = `
                                    mutation ($patches: [Patch]!) {
                                      result: updateFactSheet(id: "${id}", patches: $patches, validateOnly: false) {
                                        factSheet {
                                          ... on ITComponent {
                                            rev
                                            displayName
                                            name
                                           }
                                        }
                                      }
                                    }`
        const patches: any[] = []

        // TYPE
        patches.push({
            op: 'replace',
            path: '/category',
            value: 'software'
        })

        if (data.description && data.description.length > 0) {
            patches.push({
                op: 'replace',
                path: '/description',
                value: data.description
            })
        }

        // TECH CATEGORY
        if (data.techCategory && data.techCategory.length > 0) {
            patches.push({
                op: 'replace',
                path: '/techCategory',
                value: data.techCategory
            })
        }

        // HANDLE PROVIDER
        const field = 'provider'
        if (data[field] && data[field].length > 0) {
            if (!isUUID(data[field])) {
                const existing = await getFactSheetByNameAndType(data[field], 'Provider')
                if (existing.length > 0) {
                    data[field] = existing[0].node.id
                } else {
                    const provider = await createFactSheet(data[field], 'Provider', false)
                    data[field] = provider.createFactSheet.factSheet.id
                }
            }
        }

        if (data.provider && data.provider.length > 0) {
            patches.push({
                op: 'add',
                path: '/relITComponentToProvider/newProvider_1',
                value: data.provider
            })
        }

        data.businessAreas?.map((b: { businessArea: string, usageType: string }, idx: number) => {
            patches.push({
                op: 'add',
                path: `/relITComponentToUserGroup/newUserGroup_${idx}`,
                value: `{"factSheetId":"${b.businessArea}","usageType":"${b.usageType}" }`
            })
        })

        if (data?.phaseIn) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/phaseIn',
                value: dayjs(data.phaseIn).format('YYYY-MM-DD')
            })
        }

        if (data?.active) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/active',
                value: dayjs(data.active).format('YYYY-MM-DD')
            })
        }

        if (data?.phaseOut) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/phaseOut',
                value: dayjs(data.phaseOut).format('YYYY-MM-DD')
            })
        }

        const filter = {
            patches
        }

        return lx.executeGraphQL(mutation, JSON.stringify(filter))
    }

    const updateDesktopSoftwareFactsheet = (id: string, data: DesktopSoftware) => {
        const mutation = `
                                    mutation ($patches: [Patch]!) {
                                      result: updateFactSheet(id: "${id}", patches: $patches, validateOnly: false) {
                                        factSheet {
                                          ... on ITComponent {
                                            rev
                                            displayName
                                            name
                                           }
                                        }
                                      }
                                    }`

        const patches = []

        patches.push({
            op: 'replace',
            path: '/tags',
            value: '[{"tagId":"dae8277d-facf-4d17-ac33-52631d62f331"}]'
        })

        patches.push({op: 'replace', path: '/category', value: 'desktop'})
        patches.push({op: 'replace', path: '/packagedByGroupIT', value: data.packagedByGroupIT ? 'yes' : 'no'})

        patches.push({op: 'replace', path: '/naFields', value: '["relITComponentToProvider"]'})

        if (data?.active) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/active',
                value: dayjs(data.active).format('YYYY-MM-DD')
            })
        }

        if (data.description && data.description.length > 0) {
            patches.push({op: 'replace', path: '/description', value: data.description})
        }

        if (data.subcategory && data.subcategory.length > 0) {
            patches.push({op: 'replace', path: '/subcategory', value: data.subcategory})
        }

        if (data.guiType && data.guiType.length > 0) {
            patches.push({op: 'replace', path: '/guiType', value: data.guiType})
        }
        if (data.ring && data.ring.length > 0) {
            patches.push({op: 'replace', path: '/ring', value: data.ring})
        }
        if (data.orderable && data.orderable.length > 0) {
            patches.push({op: 'replace', path: '/orderable', value: data.orderable})
        }
        if (data.softwareProfile && data.softwareProfile.length > 0) {
            patches.push({op: 'replace', path: '/softwareProfile', value: JSON.stringify(data.softwareProfile)})
        }

        if (data.businessAreaOwner && data.businessAreaOwner.length > 0) {
            // OWNER IMMER ALS CF
            patches.push({
                op: 'add',
                path: '/relITComponentToUserGroup/newUserGroupOwner_1',
                value: `{"factSheetId":"${data.businessAreaOwner}","usageType":"owner" }`
            })
        }

        if (data.businessAreaUser && data.businessAreaUser.length > 0) {
            for (let i = 0; i < data.businessAreaUser.length; i++) {
                patches.push({
                    op: 'add',
                    path: `/relITComponentToUserGroup/newUserGroupOwner_${i + 3}`,
                    value: `{"factSheetId":"${data.businessAreaUser[i]}","usageType":"user" }`
                })
            }
        }

        if (data.desktopSoftwareReferences && data.desktopSoftwareReferences.length > 0) {
            for (let i = 0; i < data.desktopSoftwareReferences.length; i++) {
                patches.push({
                    op: 'add',
                    path: `/relRequiredByITComponentToRequiredITComponent/newReference_${i}`,
                    value: `{"factSheetId":"${data.desktopSoftwareReferences[i]}"}`
                })
            }
        }

        const filter = {
            patches
        }

        console.log('mutation', mutation)
        console.log('FILTER', filter)

        return lx.executeGraphQL(mutation, JSON.stringify(filter))
    }

    const handleMultiSelectField = (fieldName: string, oldValues: RelationValue[], newValues: string[]) => {
        const patches: { op: string, path: string, value?: string }[] = []

        oldValues.map((value) => {
            if (!newValues.includes(value.factSheetId)) {
                patches.push({
                    op: 'remove',
                    path: `/${fieldName}/${value.relationId}`
                })
            }
        })

        newValues.map((value, index) => {
            if (oldValues.filter(o => o.factSheetId === value).length === 0) {
                patches.push({
                    op: 'add',
                    path: `/${fieldName}/new_${fieldName}_${index}`,
                    value
                })
            }
        })

        return patches
    }

    const updateApplication = async (applicationId: string, data: FactSheetData, mode: 'add' | 'update', oldData?: FactSheetData) => {
        const mutation = `
mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "${applicationId}", patches: $patches, validateOnly: false) {
    factSheet {
      ... on Application {
        rev
        displayName
        name
        release
        displayName
        alias
        newurl {
          externalUrl
        }
        description
        lifecycle {
          phases {
            phase
            startDate
          }
        }
        relApplicationToDomain {
          edges {
            node {
              id
              factSheet {
                name
              }
            }
          }
        }
        relApplicationToBusinessCapability {
          edges {
            node {
              id
              factSheet {
                name
              }
            }
          }
        }
        relApplicationToUserGroup {
          edges {
            node {
              id
              factSheet {
                name
                ... on UserGroup {
                  id
                }
              }
              usageType
            }
          }
        }
      }
    }
  }
}`

        let patches: { op: string, path: string, value?: string }[] = []

        if (mode === 'add') {
            patches.push({op: 'replace', path: '/name', value: getName(data.prefix, data.name, data.external)})
        }

        // DE TAG
        if (PROD && data.prefix === 'DE-') {
            console.log('TAG ADD DE')
            patches.push({
                op: 'replace',
                path: '/tags',
                value: '[{"tagId":"d9b2daaf-ac92-4c82-a294-e7ea5b3fc312"}]'
            })
        }

        patches.push({op: 'replace', path: '/sourcing', value: data.sourcing})
        patches.push({op: 'replace', path: '/softwareType', value: data.softwareType})

        patches.push({op: 'replace', path: '/userAdmin', value: data.userAdmin})

        if (data.sourcing === 'saas') {
            if (data.saaSClassification && data.saaSClassification.length > 0) {
                patches.push({op: 'replace', path: '/saaSClassification', value: data.saaSClassification})
            }

            if (data.saaSClassificationComment && data.saaSClassificationComment.length > 0) {
                patches.push({
                    op: 'replace',
                    path: '/saaSClassificationComment',
                    value: data.saaSClassificationComment
                })
            }
        }

        if (data?.commentOnUserAdmin?.length > 0) {
            patches.push({op: 'replace', path: '/commentOnUserAdmin', value: data.commentOnUserAdmin})
        }

        patches.push({op: 'replace', path: '/userAccessControl', value: data.userAccessControl})

        if (data?.commentOnUserAccessControl?.length > 0) {
            patches.push({
                op: 'replace',
                path: '/commentOnUserAccessControl',
                value: data.commentOnUserAccessControl
            })
        }

        patches.push({
            op: 'replace',
            path: '/reachableFromNonManagedDevice',
            value: data.reachableFromNonManagedDevice
        })

        patches.push({op: 'replace', path: '/multiFactorAuthentication', value: data.multiFactorAuthentication})

        if (data?.multiFactorAuthenticationComment?.length > 0) {
            patches.push({
                op: 'replace',
                path: '/multiFactorAuthenticationComment',
                value: data.multiFactorAuthenticationComment
            })
        }

        // patches.push({"op": "replace", "path": "/needsMFA", "value": data.needsMFA});

        if (data?.plattform) {
            patches.push({op: 'replace', path: '/plattform', value: data.plattform})
        }

        patches.push({op: 'replace', path: '/guiType', value: `${JSON.stringify(data.guiType)}`})

        if (data.url?.length > 0) {
            patches.push({
                op: 'replace',
                path: '/newurl',
                value: `{"externalId": "${mode === 'add' ? 'https://' : ''}${data.url}", "comment": "${mode === 'add' ? 'https://' : ''}${data.url}"}`
            })
        }

        patches.push({op: 'replace', path: '/description', value: data.description})

        if (data?.phaseIn) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/phaseIn',
                value: dayjs(data.phaseIn).format('YYYY-MM-DD')
            })
        }

        if (data?.active) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/active',
                value: dayjs(data.active).format('YYYY-MM-DD')
            })
        }

        if (data?.phaseOut) {
            patches.push({
                op: 'replace',
                path: '/lifecycle/phaseOut',
                value: dayjs(data.phaseOut).format('YYYY-MM-DD')
            })
        }

        if (data?.hostingProviderCountry) {
            // new fields
            patches.push({
                op: 'replace',
                path: '/hostingProviderCountry',
                value: `${JSON.stringify(data.hostingProviderCountry)}`
            })
        }

        const fields = ['relApplicationToHostingProvider', 'relApplicationToOperationsProvider', 'relApplicationToSoftwareServiceProvider']

        // Sammle erst alle zu erstellenden Provider
        const providersToCreate = new Set<string>()
        for (const field of fields) {
            if (data[field]?.length) {
                data[field].forEach((value: string) => {
                    if (!isUUID(value)) {
                        providersToCreate.add(value)
                    }
                })
            }
        }

        // Erstelle Provider einmalig und speichere die IDs
        const providerNameToId = new Map()
        for (const providerName of providersToCreate) {
            const existing = await getFactSheetByNameAndType(providerName, 'Provider')
            if (existing.length > 0) {
                providerNameToId.set(providerName, existing[0].node.id)
            } else {
                const provider = await createFactSheet(providerName, 'Provider', false)
                providerNameToId.set(providerName, provider.createFactSheet.factSheet.id)
            }
        }

        // Aktualisiere die IDs in den Feldern
        for (const field of fields) {
            if (data[field]?.length) {
                data[field] = data[field].map((value: string) =>
                    isUUID(value) ? value : providerNameToId.get(value)
                )
            }
        }

        if (mode === 'add') {
            fields.map((field) => {
                const newPatches = handleMultiSelectField(field, [], data[field] ? [...data[field]] : [])
                if (newPatches.length > 0) {
                    patches = [...patches, ...newPatches]
                }
            })
        } else {
            for (const field of fields) {
                const patches = handleMultiSelectField(field, oldData![field], data[field] ? [...data[field]] : [])
                // Execute directly because of LeanIX server error: May only manipulate a relation in one of its directions in the same request
                await lx.executeGraphQL(mutation, JSON.stringify({patches})).then().catch((error) => {
                    console.log(error)
                    lx.showToastr('error', error.message)
                })
            }
        }

        if (mode === 'add') {
            patches.push({
                op: 'replace',
                path: '/applicationType',
                value: data.external ? 'externalPartnerApp' : 'application'
            })

            patches.push({op: 'add', path: '/relApplicationToDomain/newDomain_1', value: data.applicationDomain})

            if (data?.businessCapabilities) {
                patches.push({
                    op: 'add',
                    path: '/relApplicationToBusinessCapability/newBC_1',
                    value: data.businessCapabilities
                })
            }

            // NEW
            data.businessAreas?.map((b: { businessArea: string, usageType: string }, idx: number) => {
                patches.push({
                    op: 'add',
                    path: `/relApplicationToUserGroup/newUserGroup_${idx}`,
                    value: `{"factSheetId":"${b.businessArea}","usageType":"${b.usageType}" }`
                })
            })
        } else {
            if (data.url?.length == 0) {
                patches.push({
                    op: 'remove',
                    path: '/newurl'
                })
            }

            // DOMAIN
            if (!oldData!.applicationDomain) {
                patches.push({
                    op: 'add',
                    path: '/relApplicationToDomain/newDomain_1',
                    value: data.applicationDomain
                })
            } else {
                patches.push({
                    op: 'replace',
                    path: `/relApplicationToDomain/${oldData!.relApplicationToDomain.edges[0].node.id}`,
                    value: `{"factSheetId":"${data.applicationDomain}"}`
                })
            }

            // Business Capabilities
            const oldValues = oldData!.businessCapabilities
            const newValues = data.businessCapabilities

            // Löschen der Werte, die nicht mehr vorkommen
            oldValues?.forEach((value: string) => {
                if (!newValues.includes(value)) {
                    const relationId = oldData!.relApplicationToBusinessCapability.edges.filter((bc: any) => bc.node.factSheet.id === value)[0].node.id
                    patches.push({
                        op: 'remove',
                        path: `/relApplicationToBusinessCapability/${relationId}`
                    })
                }
            })

            // Hinzufügen der neuen Werte
            newValues?.forEach((value: string, index: number) => {
                if (!oldValues.includes(value)) {
                    patches.push({
                        op: 'add',
                        path: `/relApplicationToBusinessCapability/newBC_${index}`,
                        value
                    })
                }
            })

            const oldBusinessAreas = oldData!.relApplicationToUserGroup.edges.map((edge: any) => ({
                businessArea: edge.node.factSheet.id,
                usageType: edge.node.usageType,
                relationId: edge.node.id
            }))
            const newBusinessAreas = data.businessAreas

            // ADD OR UPDATE BUSINESS AREA
            newBusinessAreas.forEach((newArea: any, index: number) => {
                const existingArea = oldBusinessAreas.find((oldArea: any) => oldArea.businessArea === newArea.businessArea)

                if (!existingArea) {
                    // Add new area
                    patches.push({
                        op: 'add',
                        path: `/relApplicationToUserGroup/newBusinessArea_${index}`,
                        value: `{"factSheetId":"${newArea.businessArea}","usageType":"${newArea.usageType}"}`
                    })
                } else if (existingArea.usageType !== newArea.usageType) {
                    // Update existing area if usageType has changed
                    patches.push({
                        op: 'replace',
                        path: `/relApplicationToUserGroup/${existingArea.relationId}/usageType`,
                        value: newArea.usageType
                    })
                }
            })

            // REMOVE EXISTING AREA
            oldBusinessAreas.forEach((oldArea: any) => {
                if (!newBusinessAreas.some((newArea: any) => newArea.businessArea === oldArea.businessArea)) {
                    patches.push({
                        op: 'remove',
                        path: `/relApplicationToUserGroup/${oldArea.relationId}`
                    })
                }
            })
        }

        const filter = {
            patches
        }

        return lx.executeGraphQL(mutation, JSON.stringify(filter))
    }

    const getUserGroupDetails = async (id: string) => {
        return lx.executeGraphQL(`{factSheet(id:"${id}"){... on UserGroup {id level displayName}}}`)
    }

    const addSubscription = (applicationId: string, userId: string, roleIds: string[]) => {
        return lx.executeGraphQL(`
        mutation {
  createSubscription(factSheetId: "${applicationId}", user: {id: "${userId}"}, type: RESPONSIBLE, validateOnly: false, roleIds: ${JSON.stringify(roleIds)}) {
    id
    user {
      userName
    }
    type
    roles {
      id
      name
      subscriptionType
    }
    createdAt
    factSheet {
      id
    }
  }
}

        `)
    }

    const addObserver = async (compId: string, userId: string) => {
        return lx.executeGraphQL(`mutation m($user:UserInput!, $roles:[SubscriptionToSubscriptionRoleLinkInput])
        {createSubscription(factSheetId:"${compId}", user:$user, type:OBSERVER,
         roles:$roles){id user{id firstName lastName displayName email technicalUser permission{role status}}type 
         roles{id name description comment subscriptionType}createdAt factSheet{rev}}}`, `{
  "user": {
    "id": "${userId}"
  },
  "roles": [
    {}
  ]
}`)
    }

    const getFactSheetByName = async (name: string) => {
        return lx.executeGraphQL(`
                            query ($name: String) {
                        allFactSheets(filter: {displayName: $name}) {
                          edges {
                            node {
                              id
                              type
                              displayName
                            }
                          }
                        }
                      }
        `, JSON.stringify({
            name
        }))
    }

    const getFactSheetsByIds = async (ids: string[]) => {
        return lx.executeGraphQL(`
            query ($ids: [ID!]!) {
          allFactSheets(filter: {ids: $ids}) {
            edges {
              node {
                id
                type
                displayName
              }
            }
          }
        }
    `, JSON.stringify(
            {
                ids
            }
        ))
    }

    const createRelationsITComponent = async (compId: string, appId: string, providerId: string, name: string) => {
        return lx.executeGraphQL(`
        mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "${compId}", patches: $patches, validateOnly: false) {
    factSheet {
      ... on ITComponent {
        category
        name
        displayName
        relITComponentToApplication {
          edges {
            node {
              factSheet {
                id
                displayName
              }
            }
          }
        }
        relITComponentToProvider {
          edges {
            node {
              factSheet {
                id
                displayName
              }
            }
          }
        }
      }
    }
  }
}

        `, `
        {
 "patches": [
   {"op":"replace","path":"/category","value":"service"},
  {"op":"add","path":"/relITComponentToApplication/newApp_1","value":"${appId}"},
  {"op":"add","path":"/relITComponentToProvider/newProvider_1","value":"${providerId}"},
  {"op":"replace","path":"/name","value":"${name}"}
 
]
}



        `)
    }

    const createAppRelation = async (compId: string, appId: string) => {
        return lx.executeGraphQL(`
        mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "${compId}", patches: $patches, validateOnly: false) {
    factSheet {
      ... on ITComponent {
        category
        displayName
        relITComponentToApplication {
          edges {
            node {
              factSheet {
                id
                displayName
              }
            }
          }
        }
        relITComponentToProvider {
          edges {
            node {
              factSheet {
                id
                displayName
              }
            }
          }
        }
      }
    }
  }
}

        `, `
        {
 "patches": [
  {"op":"add","path":"/relITComponentToApplication/newApp_1","value":"${appId}"}
]
}

        `)
    }

    const getFactSheetByNameAndType = async (name: string, type: string): Promise<any[]> => {
        const existingFactSheets = await getFactSheetByName(name)

        if (existingFactSheets.allFactSheets.edges?.length > 0) {
            return existingFactSheets.allFactSheets.edges.filter((item: any) => item.node.type === type)
        } else {
            return []
        }
    }

    const getFieldValues = (factsheet: string, fieldName: string) => {
        const values: any = lx.getFactSheetFieldMetaData(factsheet, fieldName)

        const field: Field = {
            label: lx.translateField('Application', fieldName),
            values: [...Object.entries(values.values).filter(([key]) => key !== 'guiType1' && key !== 'guiType2').map(([key]) => (
                {label: lx.translateFieldValue(factsheet, fieldName, key), value: key}))]
        }
        return field
    }

    const getRelationFieldValues = (relation: string, fieldName: string) => {
        const values: any = lx.getFactSheetRelationFieldMetaData('Application', relation, fieldName)

        const field: Field = {
            label: lx.translateRelationField(relation, fieldName),
            values: [...Object.entries(values.values).map(([key]) => (
                {label: lx.translateFieldValue('Application', fieldName, key), value: key}))]
        }
        return field
    }

    const getSelectData = async (factSheetType: string) => {
        const query = `{
  allFactSheets(factSheetType: ${factSheetType}, sort: {key: "displayName"}) {
    edges {
      node {
        id
        displayName
      }
    }
  }
}
`

        const data = await lx.executeGraphQL(query)

        const parsed: Value[] = [...data.allFactSheets.edges.map((d: any) => ({
            label: d.node.displayName,
            value: d.node.id
        }))]

        return parsed
    }

    const getTags = async (tagGroupId: string) => {
        const query = `
                        {
                      tagGroup(id:"${tagGroupId}"){
                        tags{
                          edges{
                            node {
                              id
                              name
                            }
                          }
                        }
                      }
                    }
    `

        const data = await lx.executeGraphQL(query)

        const parsed: Value[] = [...data.tagGroup.tags.edges.map((d: any) => ({
            label: d.node.name,
            value: d.node.id
        }))]

        return parsed
    }

    const removeFactSheet = async (id: string) => {
        return lx.executeGraphQL(`
        mutation ($patches: [Patch]!) {
  result: updateFactSheet(id: "${id}", comment: "Delete FS", patches: $patches, validateOnly: false) {
    factSheet {
    
        status
        rev
        type
      
    }
  }
}

        `, `{
 "patches":[{"op":"add","path":"/status","value":"ARCHIVED"}]
}`)
    }

    const getApplications = async () => {
        const result = await lx.executeGraphQL(`{
                app: allFactSheets(filter: {facetFilters: [{facetKey: "FactSheetTypes", keys: ["Application"]}]}) {
                  totalCount
                  edges {
                    node {
                      id
                      displayName
                      name
                      fullName
                      type
                    }
                  }
                }
              }`)
        const filterItems: Value[] = []
        for (const app of result.app.edges) {
            filterItems.push(({label: app.node.name, value: app.node.id}))
        }
        return filterItems
    }

    const getAppDataById = async (applicationId: string): Promise<DesktopSoftwareAppData | undefined> => {
        const data = await lx.executeGraphQL(`
                                {
                          factSheet(id: "${applicationId}") {
                            ... on Application {
                              id
                              name
                              guiType
                              description
                              businessCriticality
                              subscriptions {
                                edges {
                                  node {
                                    user {
                                      id
                                      userName
                                      firstName
                                      lastName
                                    }
                                    roles {
                                      id
                                      name
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
        `)

        if (data && data.factSheet) {
            const applicationOwnerRoleID = SUB_APPLICATION_OWNER_TECHNICAL // Application Owner (technical)
            const applicationResponsibleRoleID = SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL // Application Responsible (functional)

            const applicationsOwners: Value[] = []
            const applicationResponsibles: Value[] = []

            data.factSheet.subscriptions.edges.forEach((edge: any) => {
                const userId = edge.node.user.id
                const roles = edge.node.roles.map((role: any) => role.id)

                if (roles.includes(applicationOwnerRoleID)) {
                    applicationsOwners.push({
                        label: `${edge.node.user.firstName} ${edge.node.user.lastName}`,
                        value: userId
                    })
                }

                if (roles.includes(applicationResponsibleRoleID)) {
                    applicationResponsibles.push({
                        label: `${edge.node.user.firstName} ${edge.node.user.lastName}`,
                        value: userId
                    })
                }
            })
            // console.log(data.factSheet.guiType)
            return {
                id: data.factSheet.id,
                description: data.factSheet.description,
                ring: getRingByBusinessCriticality(data.factSheet.businessCriticality),
                guiType: data.factSheet.guiType ? data.factSheet.guiType?.filter((g: string) => g === 'rich' || g === 'web') : [],
                applicationsOwners,
                applicationResponsibles
            }
        }

        return undefined
    }

    const updateUserSubscription = async (subId: string, userId: string, roles: string[]) => {
        const filter = {
            roles: [...roles.map(r => ({id: r, comment: ''}))]
        }

        return lx.executeGraphQL(`
                mutation m($roles: [SubscriptionToSubscriptionRoleLinkInput]) {
  updateSubscription(
    id: "${subId}"
    user: {id: "${userId}"}
    type: RESPONSIBLE
    roles: $roles
  ) {
    id
    user {
      id
      firstName
      lastName
      displayName
      email
      technicalUser
      permission {
        role
        status
      }
    }
    type
    roles {
      id
      name
      comment
    }
    createdAt
    factSheet {
      rev
    }
  }
}
        `, JSON.stringify(filter))
    }

    const removeSubscription = async (subId: string) => lx.executeGraphQL(`mutation {deleteSubscription(id:"${subId}"){id}}`)

    const removeRelation = (appId: string, compId: string) => {
        const filter = {
            patches: [
                {
                    op: 'remove',
                    path: `/relApplicationToITComponent/${compId}`
                }
            ]
        }

        return lx.executeGraphQL(`
                            mutation ($patches: [Patch]!) {
  updateFactSheet(id: "${appId}", patches: $patches) {
    factSheet {
      id
      name
      ... on Application {
        relApplicationToITComponent {
          edges {
            node {
              id
            }
          }
        }
      }
    }
  }
}
            `, JSON.stringify(filter))
    }

    const saveChangesApplication = async (appId: string, appData: FactSheetData, oldData: FactSheetData) => {
        // console.log(appId, appData, oldData)

        const updatedApp = await updateApplication(appId, appData, 'update', oldData)
        console.log('updated app', updatedApp)

        // console.log("OLD PROVIDER", oldData.provider, appData.providerName);

        if (oldData.provider !== appData.providerName) {
            // Provider has changed
            // console.log("provider got changed")
            const oldProvider: string | undefined = getProviderITComponentFromApplication(oldData.sourcing, oldData.relApplicationToITComponent)
            // console.log("OLD PROVIDER", oldProvider);
            if (oldProvider && oldProvider.length > 0) {
                const removed = await removeRelation(appId, oldProvider)
                // console.log("REMOVED", removed);
            }
            const updated = await handleThirdPartySoftware(appId, appData)
            // console.log("UPDATED provider", updated)
        }

        const updatedUsers: string[] = []
        if (oldData.applicationOwnerTechnical.length > 0) {
            for (let i = 0; i < oldData.applicationOwnerTechnical.length; i++) {
                const userId = oldData.applicationOwnerTechnical[i]
                const roleIds = getAllRoleIdsByUserId(oldData.subscriptionsRaw, userId)
                // console.log("ROLE IDS", roleIds, userId);

                const relevantRoles = roleIds.roleIds.filter(r => r !== SUB_APPLICATION_OWNER_TECHNICAL)

                if (appData.applicationOwnerTechnical.includes(userId)) {
                    relevantRoles.push(SUB_APPLICATION_OWNER_TECHNICAL)
                }
                if (appData.applicationResponsibleFunctional.includes(userId)) {
                    relevantRoles.push(SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL)
                }

                if (relevantRoles.length === 0) {
                    const deleted = await removeSubscription(roleIds.subId)
                    // console.log("DELETED ROLE IDS", deleted);
                } else {
                    const update = await updateUserSubscription(roleIds.subId, userId, relevantRoles)
                    // console.log("UPDATE", update);
                }
                updatedUsers.push(userId)
            }
        }

        if (oldData.applicationResponsibleFunctional.length > 0) {
            for (let i = 0; i < oldData.applicationResponsibleFunctional.length; i++) {
                const userId = oldData.applicationResponsibleFunctional[i]
                const roleIds = getAllRoleIdsByUserId(oldData.subscriptionsRaw, userId)
                // console.log("ROLE IDS", roleIds, userId);

                const relevantRoles = roleIds.roleIds.filter(r => r !== SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL)

                if (appData.applicationOwnerTechnical.includes(userId)) {
                    relevantRoles.push(SUB_APPLICATION_OWNER_TECHNICAL)
                }
                if (appData.applicationResponsibleFunctional.includes(userId)) {
                    relevantRoles.push(SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL)
                }

                if (relevantRoles.length === 0) {
                    const deleted = await removeSubscription(roleIds.subId)
                    // console.log("DELETED ROLE IDS", deleted);
                } else {
                    const update = await updateUserSubscription(roleIds.subId, userId, relevantRoles)
                    // console.log("update", update);
                }
                updatedUsers.push(userId)
            }
        }

        console.log('FINALLY ADD NEW SUBS FOR1', appData.applicationOwnerTechnical.filter((u: string) => !oldData.applicationOwnerTechnical.includes(u)))
        console.log('FINALLY ADD NEW SUBS FOR2', appData.applicationResponsibleFunctional.filter((u: string) => !oldData.applicationResponsibleFunctional.includes(u)))

        const subscriptionsCreated = await addSubscriptions(appId, appData.applicationOwnerTechnical.filter((u: string) => !updatedUsers.includes(u)), appData.applicationResponsibleFunctional.filter((u: string) => !updatedUsers.includes(u)))

        console.log('subscriptionsCreated', subscriptionsCreated)
        // getRoleIdByRoleNameAndUserId()
    }

    const handleThirdPartySoftware = async (appId: string, appData: FactSheetData) => {
        let createdITComponent = ''
        let createdProvider = ''
        let additionalStuffOk = false

        // ADDITIONAL STUFF
        if (appData.softwareType === 'thirdParty') {
            let compID = ''

            let existingComponent = false

            if (appData.sourcing === 'saas') {
                // IT Component <name> Software as a Service

                const name = `${appData.providerName} Software as a Service`

                const existing = await getFactSheetByNameAndType(name, 'ITComponent')
                if (existing.length > 0) {
                    compID = existing[0].node.id
                    existingComponent = true
                } else {
                    const itComponent = await createFactSheet(name, 'ITComponent', false)
                    compID = itComponent.createFactSheet.factSheet.id
                    createdITComponent = compID
                }
            } else {
                // IT Component <name> Software and Support
                const name = `${appData.providerName} Software and Support`

                const existing = await getFactSheetByNameAndType(name, 'ITComponent')
                if (existing.length > 0) {
                    compID = existing[0].node.id
                    existingComponent = true
                } else {
                    const itComponent = await createFactSheet(name, 'ITComponent', false)
                    compID = itComponent.createFactSheet.factSheet.id
                    createdITComponent = compID
                }
            }

            // Link zu Provider and Types
            let providerID = ''
            const existingProvider = await getFactSheetByNameAndType(appData.providerName, 'Provider')

            if (existingProvider.length > 0) {
                providerID = existingProvider[0].node.id
            } else {
                const prov = await createFactSheet(appData.providerName, 'Provider', false)
                providerID = prov.createFactSheet.factSheet.id
                createdProvider = providerID
            }

            if (existingProvider.length > 0 && existingComponent) {
                // NUR RELATION APP
                const relations = await createAppRelation(compID, appId)

                if (relations.result) {
                    additionalStuffOk = true
                }
            } else {
                const relations = await createRelationsITComponent(compID, appId, providerID, appData.sourcing == 'saas'
                    ? 'Software as a Service'
                    : 'Software and Support')

                if (relations.result) {
                    additionalStuffOk = true
                }
            }
        } else {
            additionalStuffOk = true
        }

        return {
            createdITComponent,
            createdProvider,
            additionalStuffOk
        }
    }

    const addSubscriptions = async (appId: string, appOwners: string[], appResponsibles: string[]) => {
        // ADD Subscriptions
        let subscriptionsCreated = false

        // Create a Set to store unique appOwner and appResponsible combinations
        const uniqueSubscriptions = new Map<string, Set<string>>()

        // Function to add roles to a person in the Map
        const addRolesToPerson = (person: string, roles: string[]) => {
            if (!uniqueSubscriptions.has(person)) {
                uniqueSubscriptions.set(person, new Set())
            }
            roles.forEach(role => uniqueSubscriptions.get(person)?.add(role))
        }

        // Add roles for appOwners
        appOwners.forEach((owner) => {
            addRolesToPerson(owner, [SUB_APPLICATION_OWNER_TECHNICAL])
        })

        // Add roles for appResponsibles
        appResponsibles.forEach((responsible) => {
            addRolesToPerson(responsible, [SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL])
        })

        // Process each unique subscription
        for (const [person, rolesSet] of uniqueSubscriptions.entries()) {
            const rolesArray = Array.from(rolesSet)
            console.log('ADD SUB', appId, person, rolesArray)
            try {
                const result = await addSubscription(appId, person, rolesArray)
                if (result?.createSubscription) {
                    subscriptionsCreated = true
                } else {
                    subscriptionsCreated = false
                    break // If any subscription fails, break out of the loop
                }
            } catch (error) {
                console.log('ERROR WHILE CREATING SUB', error)
            }
        }

        console.log('SUB CREATED', subscriptionsCreated)

        return subscriptionsCreated
    }

    const getProviderITComponentFromApplication = (sourcing: string, relITComponents: any) => {
        const searchParam = sourcing === 'saas' ? 'Software as a Service' : 'Software and Support'

        const relevantItems = relITComponents.edges.filter((item: any) =>
            item.node.factSheet.name.toLowerCase().includes(searchParam.toLowerCase()))

        console.log('RELEVANT items', relevantItems)

        if (relevantItems.length > 0) {
            console.log('relevant items:', relevantItems[0].node.id)
            return relevantItems[0].node.id
        }
        return undefined
    }

    const saveApplicationWithAllData = async (appData: FactSheetData) => {
        console.log('APP DATA', appData)
        let createdApp = ''
        const createdITComponent = ''
        const createdProvider = ''

        try {
            const appName = getName(appData.prefix, appData.name, appData.external)

            console.log('SAVE APP:', appName)
            const data = await createFactSheet(appName, 'Application', false)
            console.log('SAVED APPLICATION', data)

            if (data?.createFactSheet?.factSheet?.id) {
                createdApp = data.createFactSheet.factSheet.id
                const app = await updateApplication(createdApp, appData, 'add')

                const subscriptionsCreated = await addSubscriptions(createdApp, appData.applicationOwnerTechnical, appData.applicationResponsibleFunctional)

                if (app.result.factSheet && subscriptionsCreated) {
                    return createdApp
                } else {
                    return ''
                }
            } else {
                return ''
            }
        } catch (e: any) {
            console.log('ERROR WHILE saveApplicationWithAllData', e)

            lx.showToastr('error', e.message)

            if (createdApp.length > 0) {
                const removedApp = await removeFactSheet(createdApp)
                console.log('REMOVED APP ', removedApp)
            }

            if (createdITComponent.length > 0) {
                const removedITComponent = await removeFactSheet(createdITComponent)
                console.log('REMOVED IT COMPONENT ', removedITComponent)
            }

            if (createdProvider.length > 0) {
                const removedProvider = await removeFactSheet(createdProvider)
                console.log('REMOVED PROVIDER', removedProvider)
            }
            return ''
        }
    }

    const getApplicationDetails = async (appId: string) => {
        const data = await lx.executeGraphQL(`{
  factSheet(id: "${appId}") {
    ... on Application {
      id
      name
      description
      plattform
      hostingProviderCountry
      sourcing
      saaSClassification
      saaSClassificationComment
      softwareType
      guiType
      newurl {
        comment
        externalId
        externalUrl
        externalVersion
        status
      }
      lifecycle {
        phases {
          phase
          startDate
        }
      }
      relApplicationToHostingProvider {
        edges {
          node {
            id
            factSheet {
              id
              name
            }
          }
        }
      }
      relApplicationToOperationsProvider {
        edges {
          node {
            id
            factSheet {
              id
              name
            }
          }
        }
      }
      relApplicationToSoftwareServiceProvider {
        edges {
          node {
            id
            factSheet {
              id
              name
            }
          }
        }
      }
      relApplicationToDomain {
        edges {
          node {
            id
            factSheet {
              id
              name
            }
          }
        }
      }
      relApplicationToBusinessCapability {
        edges {
          node {
            id
            factSheet {
              id
              name
            }
          }
        }
      }
      relApplicationToUserGroup {
        edges {
          node {
            id
            usageType
            factSheet {
              id
              name
              displayName
              level
            }
          }
        }
      }
      userAdmin
      commentOnUserAdmin
      userAccessControl
      commentOnUserAccessControl
      reachableFromNonManagedDevice
      multiFactorAuthentication
      multiFactorAuthenticationComment
      subscriptions {
        edges {
          node {
            id
            roles {
              id
              name
            }
            user {
              id
              firstName
              lastName
              role
            }
          }
        }
      }
      relApplicationToITComponent {
        edges {
          node {
            id
            factSheet {
              ... on ITComponent {
                id
                name
                displayName
                relITComponentToProvider {
                  edges {
                    node {
                      factSheet {
                        id
                        name
                        displayName
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}`)

        // life cycle function
        const getLifeCycleDateByPhase = (key: string) => {
            const result = (data.factSheet.lifecycle ? data.factSheet.lifecycle.phases : []).filter((p: {
                phase: string
                startDate: string
            }) => p.phase === key)
            if (result.length > 0) {
                return result[0].startDate
            } else {
                return undefined
            }
        }

        const getSingleRelationValue = (relAppDomain: any) => {
            return relAppDomain.edges.length > 0 ? relAppDomain.edges[0].node.factSheet.id : undefined
        }

        const getMultiRelationValue = (relAppDomain: any) => {
            const values: string[] = []
            relAppDomain.edges.forEach((e: any) => values.push(e.node.factSheet.id))
            return values
        }

        const getBusinessAreaParsed = (relApplicationToUserGroup: any): {
            businessArea: string
            usageType: string
        }[] => {
            return (relApplicationToUserGroup.edges.map((e: any) => ({
                businessArea: e.node.factSheet.id,
                usageType: e.node.usageType,
                existing: true
            })) as { businessArea: string, usageType: string }[])
                .sort((a, b) => (a.usageType || 'z').localeCompare(b.usageType || 'z'))
        }

        const getUserGroup = (relApplicationToUserGroup: any, type: 'owner' | 'user' | null): string | string[] | {
            relationId: string
            factSheetId: string
        }[] | undefined => {
            if (type === 'owner') {
                const ownerEdges = relApplicationToUserGroup.edges.filter((edge: any) => edge.node.usageType === type)

                if (ownerEdges.length > 0) {
                    return ownerEdges.sort((a: any, b: any) => b.node.factSheet.displayName.length - (a.node.factSheet.displayName.length))[0].node.factSheet.id
                }
            } else if (type === 'user') {
                const user = relApplicationToUserGroup.edges.filter((edge: any) => edge.node.usageType === type)
                if (user.length > 0) {
                    console.log(user)
                    return [...user.map((u: any) => (u.node.factSheet.id))]
                }
            } else if (type === null) {
                const user = relApplicationToUserGroup.edges.filter((edge: any) => edge.node.usageType === type)
                if (user.length > 0) {
                    console.log(user)
                    return [...user.map((u: any) => ({
                        relationId: u.node.id,
                        factSheetId: u.node.factSheet.id
                    }))]
                }
            }
            return undefined
        }

        const getApplicationRoles = (subscriptions: any): {
            applicationResponsible: string[]
            applicationOwnerTechnical: string[]
        } => {
            const applicationResponsibleRole = 'Application Responsible (functional)'
            const applicationOwnerTechnicalRole = 'Application Owner (technical)'

            const result: { applicationResponsible: string[], applicationOwnerTechnical: string[] } = {
                applicationResponsible: [],
                applicationOwnerTechnical: []
            }

            subscriptions.edges.forEach((edge: any) => {
                const user = edge.node.user
                const roles = edge.node.roles

                roles.forEach((role: any) => {
                    if (user.id !== '00000000-0000-0000-0000-000000000000') {
                        if (role.name === applicationResponsibleRole) {
                            result.applicationResponsible.push(user.id)
                        }
                        if (role.name === applicationOwnerTechnicalRole) {
                            result.applicationOwnerTechnical.push(user.id)
                        }
                    }
                })
            })

            return result
        }

        const roles = getApplicationRoles(data.factSheet.subscriptions)

        const parseRelationValue = (relation: any) => {
            const relationValues: RelationValue[] = []

            relation.edges.forEach((r: any) => {
                relationValues.push({
                    relationId: r.node.id,
                    factSheetId: r.node.factSheet.id
                })
            })
            return relationValues
        }

        return {
            id: data.factSheet.id,
            name: data.factSheet.name,
            description: data.factSheet.description,
            plattform: data.factSheet.plattform,
            sourcing: data.factSheet.sourcing,
            saaSClassification: data.factSheet.saaSClassification,
            saaSClassificationComment: data.factSheet.saaSClassificationComment,
            softwareType: data.factSheet.softwareType,
            guiType: data.factSheet.guiType,
            url: data.factSheet.newurl?.externalUrl,
            phaseIn: getLifeCycleDateByPhase('phaseIn'),
            active: getLifeCycleDateByPhase('active'),
            phaseOut: getLifeCycleDateByPhase('phaseOut'),
            applicationDomain: getSingleRelationValue(data.factSheet.relApplicationToDomain),
            businessCapabilities: getMultiRelationValue(data.factSheet.relApplicationToBusinessCapability),
            businessAreaOwner: getUserGroup(data.factSheet.relApplicationToUserGroup, 'owner'),
            businessAreaUsers: getUserGroup(data.factSheet.relApplicationToUserGroup, 'user'),
            businessAreaUndefined: getUserGroup(data.factSheet.relApplicationToUserGroup, null),
            businessAreaParsed: getBusinessAreaParsed(data.factSheet.relApplicationToUserGroup),
            userAdmin: data.factSheet.userAdmin,
            commentOnUserAdmin: data.factSheet.commentOnUserAdmin,
            userAccessControl: data.factSheet.userAccessControl,
            commentOnUserAccessControl: data.factSheet.commentOnUserAccessControl,
            reachableFromNonManagedDevice: data.factSheet.reachableFromNonManagedDevice,
            multiFactorAuthentication: data.factSheet.multiFactorAuthentication,
            multiFactorAuthenticationComment: data.factSheet.multiFactorAuthenticationComment,
            // needsMFA: data.factSheet.needsMFA,
            // provider: getProviderFromApplication(data.factSheet.sourcing, data.factSheet.relApplicationToITComponent),
            applicationOwnerTechnical: roles.applicationOwnerTechnical,
            applicationResponsibleFunctional: roles.applicationResponsible,
            subscriptionsRaw: data.factSheet.subscriptions,
            relApplicationToITComponent: data.factSheet.relApplicationToITComponent,
            relApplicationToDomain: data.factSheet.relApplicationToDomain,
            relApplicationToBusinessCapability: data.factSheet.relApplicationToBusinessCapability,
            relApplicationToUserGroup: data.factSheet.relApplicationToUserGroup,

            hostingProviderCountry: data.factSheet.hostingProviderCountry,
            relApplicationToHostingProvider: parseRelationValue(data.factSheet.relApplicationToHostingProvider),
            relApplicationToOperationsProvider: parseRelationValue(data.factSheet.relApplicationToOperationsProvider),
            relApplicationToSoftwareServiceProvider: parseRelationValue(data.factSheet.relApplicationToSoftwareServiceProvider)
        }
    }


    return {
        createFactSheet,
        updateApplication,
        getFactSheetByName,
        createRelationsITComponent,
        getFactSheetByNameAndType,
        getProviders,
        getFieldValues,
        getRelationFieldValues,
        getSelectData,
        createAppRelation,
        addSubscription,
        removeFactSheet,
        validateName,
        updateDesktopSoftwareFactsheet,
        addObserver,
        getApplications,
        getAppDataById,
        saveApplicationWithAllData,
        getApplicationDetails,
        saveChangesApplication,
        getDesktopSoftware,
        updateServerSoftwareFactsheet,
        getTags,
        getFactSheetsByIds,
        updateEUT,
    }
}
